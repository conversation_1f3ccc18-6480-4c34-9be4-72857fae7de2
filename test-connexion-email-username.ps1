# Test de connexion par email et username
Write-Host "=== Test Connexion Email et Username ===" -ForegroundColor Green

$randomId = Get-Random -Minimum 1000 -Maximum 9999
$testEmail = "testconnexion$<EMAIL>"
$testUsername = "testconnexion$randomId"

# Etape 1: Creer un utilisateur de test
Write-Host "`n1. Creation utilisateur de test" -ForegroundColor Yellow
try {
    $createBody = @{
        email = $testEmail
        username = $testUsername
        password = "Test123!"
        firstName = "Test"
        lastName = "Connexion"
    } | ConvertTo-Json

    $createResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/register" -Method POST -Body $createBody -ContentType "application/json"
    Write-Host "OK - Utilisateur cree: $testUsername" -ForegroundColor Green
} catch {
    Write-Host "ERREUR - Creation utilisateur: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Attendre un peu pour que l'utilisateur soit bien enregistre
Start-Sleep -Seconds 2

# Etape 2: Test connexion avec EMAIL
Write-Host "`n2. Test connexion avec EMAIL" -ForegroundColor Yellow
try {
    $emailLoginBody = @{
        email = $testEmail
        password = "Test123!"
    } | ConvertTo-Json

    $emailLoginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $emailLoginBody -ContentType "application/json"
    Write-Host "OK - Connexion par EMAIL reussie!" -ForegroundColor Green
    Write-Host "Token: $($emailLoginResponse.token.Substring(0, 30))..." -ForegroundColor Gray
    Write-Host "Username: $($emailLoginResponse.user.username)" -ForegroundColor Gray
} catch {
    Write-Host "ERREUR - Connexion par EMAIL: $($_.Exception.Message)" -ForegroundColor Red
}

# Etape 3: Test connexion avec USERNAME
Write-Host "`n3. Test connexion avec USERNAME" -ForegroundColor Yellow
try {
    $usernameLoginBody = @{
        username = $testUsername
        password = "Test123!"
    } | ConvertTo-Json

    $usernameLoginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $usernameLoginBody -ContentType "application/json"
    Write-Host "OK - Connexion par USERNAME reussie!" -ForegroundColor Green
    Write-Host "Token: $($usernameLoginResponse.token.Substring(0, 30))..." -ForegroundColor Gray
    Write-Host "Username: $($usernameLoginResponse.user.username)" -ForegroundColor Gray
} catch {
    Write-Host "ERREUR - Connexion par USERNAME: $($_.Exception.Message)" -ForegroundColor Red
}

# Etape 4: Test connexion admin par EMAIL
Write-Host "`n4. Test connexion admin par EMAIL" -ForegroundColor Yellow
try {
    $adminEmailBody = @{
        email = "<EMAIL>"
        password = "Admin123!"
    } | ConvertTo-Json

    $adminEmailResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $adminEmailBody -ContentType "application/json"
    Write-Host "OK - Connexion admin par EMAIL reussie!" -ForegroundColor Green
    Write-Host "Token: $($adminEmailResponse.token.Substring(0, 30))..." -ForegroundColor Gray
    Write-Host "Username: $($adminEmailResponse.user.username)" -ForegroundColor Gray
} catch {
    Write-Host "ERREUR - Connexion admin par EMAIL: $($_.Exception.Message)" -ForegroundColor Red
}

# Etape 5: Test connexion admin par USERNAME
Write-Host "`n5. Test connexion admin par USERNAME" -ForegroundColor Yellow
try {
    $adminUsernameBody = @{
        username = "admin"
        password = "Admin123!"
    } | ConvertTo-Json

    $adminUsernameResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $adminUsernameBody -ContentType "application/json"
    Write-Host "OK - Connexion admin par USERNAME reussie!" -ForegroundColor Green
    Write-Host "Token: $($adminUsernameResponse.token.Substring(0, 30))..." -ForegroundColor Gray
    Write-Host "Username: $($adminUsernameResponse.user.username)" -ForegroundColor Gray
} catch {
    Write-Host "ERREUR - Connexion admin par USERNAME: $($_.Exception.Message)" -ForegroundColor Red
}

# Etape 6: Test avec les anciens comptes crees
Write-Host "`n6. Test avec anciens comptes" -ForegroundColor Yellow

# Test utilisateur precedent
try {
    $oldUserBody = @{
        email = "<EMAIL>"
        password = "User123!"
    } | ConvertTo-Json

    $oldUserResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $oldUserBody -ContentType "application/json"
    Write-Host "OK - Ancien utilisateur par EMAIL: testuser1732" -ForegroundColor Green
} catch {
    Write-Host "ERREUR - Ancien utilisateur par EMAIL: $($_.Exception.Message)" -ForegroundColor Red
}

# Test vendeur precedent
try {
    $oldSellerBody = @{
        email = "<EMAIL>"
        password = "Seller123!"
    } | ConvertTo-Json

    $oldSellerResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $oldSellerBody -ContentType "application/json"
    Write-Host "OK - Ancien vendeur par EMAIL: testseller1732" -ForegroundColor Green
} catch {
    Write-Host "ERREUR - Ancien vendeur par EMAIL: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== RESUME DES TESTS ===" -ForegroundColor Green
Write-Host "Nouveau compte cree pour test:" -ForegroundColor White
Write-Host "Email: $testEmail" -ForegroundColor Cyan
Write-Host "Username: $testUsername" -ForegroundColor Cyan
Write-Host "Password: Test123!" -ForegroundColor Cyan

Write-Host "`nComptes existants:" -ForegroundColor White
Write-Host "Admin: <EMAIL> / admin / Admin123!" -ForegroundColor Yellow
Write-Host "User: <EMAIL> / testuser1732 / User123!" -ForegroundColor Cyan
Write-Host "Seller: <EMAIL> / testseller1732 / Seller123!" -ForegroundColor Cyan
