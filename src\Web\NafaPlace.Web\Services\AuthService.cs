using System.Net.Http.Json;
using System.Security.Claims;
using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using NafaPlace.Web.Models.Auth;
using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;

namespace NafaPlace.Web.Services;

public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly CustomAuthStateProvider _authStateProvider;

    // Event to notify authentication state changes
    public event Action? AuthenticationStateChanged;

    public AuthService(HttpClient httpClient, ILocalStorageService localStorage, CustomAuthStateProvider authStateProvider)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _authStateProvider = authStateProvider;
    }

    public async Task<AuthResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            Console.WriteLine($"Tentative de connexion avec l'utilisateur: {request.Email}");
            
            // Créer une requête adaptée au format attendu par l'API
            var apiRequest = new
            {
                Email = request.Email,
                Username = string.Empty, // Laisser vide pour utiliser l'email
                Password = request.Password
            };
            
            // Envoyer la requête au format attendu par l'API
            var response = await _httpClient.PostAsJsonAsync("api/auth/login", apiRequest);

            Console.WriteLine($"Réponse du serveur: {response.StatusCode}");
            
            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponse>();
                if (authResponse != null)
                {
                    Console.WriteLine("Connexion réussie, récupération du token");
                    
                    // Déterminer quel token utiliser (AccessToken ou Token)
                    string token = !string.IsNullOrEmpty(authResponse.AccessToken) 
                        ? authResponse.AccessToken 
                        : authResponse.Token ?? string.Empty;
                    
                    Console.WriteLine($"Token récupéré: {(string.IsNullOrEmpty(token) ? "Non" : "Oui")}");
                    
                    if (!string.IsNullOrEmpty(token))
                    {
                        // Supprimer les guillemets éventuels autour du token avant de le stocker
                        token = token.Trim('"');
                        
                        // Stocker le token dans le localStorage
                        await _localStorage.SetItemAsync("authToken", token);
                        
                        // Mettre à jour l'état d'authentification
                        _authStateProvider.MarkUserAsAuthenticated(token);
                        
                        // Déclencher l'événement de changement d'état d'authentification
                        AuthenticationStateChanged?.Invoke();
                        
                        // S'assurer que le Token est défini pour la vérification dans Login.razor
                        authResponse.Token = token;
                        authResponse.Success = true;
                        
                        Console.WriteLine("État d'authentification mis à jour");
                        return authResponse;
                    }
                    else
                    {
                        Console.WriteLine("Erreur: Token vide reçu de l'API");
                    }
                }
            }
            else
            {
                Console.WriteLine($"Échec de la connexion. Code: {response.StatusCode}");
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Contenu de l'erreur: {errorContent}");
                
                return new AuthResponse 
                { 
                    Success = false, 
                    Message = errorContent 
                };
            }
            
            return new AuthResponse 
            { 
                Success = false, 
                Message = "Échec de la connexion. Veuillez vérifier vos identifiants." 
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la connexion: {ex.Message}");
            return new AuthResponse 
            { 
                Success = false, 
                Message = $"Erreur: {ex.Message}" 
            };
        }
    }

    public async Task<AuthResponse> RegisterAsync(RegisterRequest registerRequest)
    {
        try
        {
            // Adapter le modèle RegisterRequest du web au modèle API
            var apiRequest = new
            {
                Email = registerRequest.Email,
                Username = registerRequest.Username,
                Password = registerRequest.Password,
                FirstName = registerRequest.FirstName,
                LastName = registerRequest.LastName
            };
            
            Console.WriteLine($"Envoi de la demande d'inscription pour {apiRequest.Email}");
            
            var response = await _httpClient.PostAsJsonAsync("api/auth/register", apiRequest);
            
            Console.WriteLine($"Réponse du serveur: {response.StatusCode}");
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<AuthResponse>();
                
                if (result != null)
                {
                    // Ne pas stocker le token ni notifier le CustomAuthStateProvider
                    // pour éviter l'authentification automatique après l'inscription
                    Console.WriteLine("Inscription réussie, mais pas d'authentification automatique");
                    
                    // S'assurer qu'aucun token n'est stocké dans le localStorage
                    await _localStorage.RemoveItemAsync("authToken");
                    
                    // Forcer l'état de déconnexion
                    _authStateProvider.MarkUserAsLoggedOut();
                    
                    return result;
                }
            }
            
            // Gérer les erreurs de validation ou autres erreurs
            if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Erreur lors de l'inscription: {errorContent}");
                
                return new AuthResponse
                {
                    Success = false,
                    Message = errorContent
                };
            }
            
            var genericErrorContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Erreur générique lors de l'inscription: {genericErrorContent}");
            
            return new AuthResponse
            {
                Success = false,
                Message = genericErrorContent
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception dans RegisterAsync: {ex.Message}");
            return new AuthResponse
            {
                Success = false,
                Message = $"Une erreur s'est produite lors de l'inscription: {ex.Message}"
            };
        }
    }

    public async Task Logout()
    {
        await _localStorage.RemoveItemAsync("authToken");
        _authStateProvider.MarkUserAsLoggedOut();
        _httpClient.DefaultRequestHeaders.Authorization = null;
        AuthenticationStateChanged?.Invoke(); // Notify state change
    }

    public async Task<UserDto> GetCurrentUserAsync()
    {
        try
        {
            // Récupérer le token depuis le localStorage
            var token = await _localStorage.GetItemAsync<string>("authToken");
            
            if (string.IsNullOrEmpty(token))
            {
                Console.WriteLine("Aucun token trouvé dans le localStorage");
                return new UserDto { IsAuthenticated = false };
            }
            
            // Extraire les claims du token JWT
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadToken(token) as JwtSecurityToken;
            
            if (jsonToken == null)
            {
                Console.WriteLine("Token JWT invalide");
                return new UserDto { IsAuthenticated = false };
            }
            
            try
            {
                var userIdClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "sub");
                if (userIdClaim == null)
                {
                    Console.WriteLine("Claim 'sub' non trouvée dans le token");
                    return new UserDto { IsAuthenticated = false };
                }
                
                // Convertir en int
                var userId = int.Parse(userIdClaim.Value);
                
                // Récupérer les informations complètes de l'utilisateur depuis l'API
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var response = await _httpClient.GetAsync($"api/users/profile");
                
                if (response.IsSuccessStatusCode)
                {
                    var userProfile = await response.Content.ReadFromJsonAsync<UserDto>();
                    if (userProfile != null)
                    {
                        userProfile.IsAuthenticated = true;
                        return userProfile;
                    }
                }
                
                // Si l'appel API échoue, créer un UserDto minimal à partir des claims
                var usernameClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name || c.Type == "unique_name");
                var username = usernameClaim?.Value ?? "Utilisateur";
                
                var emailClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email || c.Type == "email");
                var email = emailClaim?.Value ?? "";
                
                var roleClaims = jsonToken.Claims.Where(c => c.Type == ClaimTypes.Role || c.Type == "role" || c.Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/role");
                var roles = roleClaims.Select(c => c.Value).ToList();
                
                // Créer un UserDto à partir des claims
                var user = new UserDto
                {
                    Id = userId, // Utiliser l'entier
                    Username = username,
                    Email = email,
                    Roles = roles,
                    IsAuthenticated = true
                };
                
                return user;
            }
            catch (Exception ex) when (ex is JsonException || ex is FormatException)
            {
                Console.WriteLine($"Erreur lors de la désérialisation JSON ou du format: {ex.Message}");
                return new UserDto { IsAuthenticated = false };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'extraction des claims: {ex.Message}");
                return new UserDto { IsAuthenticated = false };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception dans GetCurrentUserAsync: {ex.Message}");
            return new UserDto { IsAuthenticated = false };
        }
    }

    public async Task<UserDto> UpdateUserProfileAsync(UserDto user)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return new UserDto { IsAuthenticated = false };
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Créer l'objet de requête pour la mise à jour du profil
            var updateRequest = new
            {
                FirstName = user.FirstName,
                LastName = user.LastName,
                PhoneNumber = user.PhoneNumber
            };

            // Appeler l'API pour mettre à jour le profil utilisateur
            var response = await _httpClient.PutAsJsonAsync("api/users/profile", updateRequest);

            if (response.IsSuccessStatusCode)
            {
                // Mettre à jour l'état d'authentification pour refléter les changements
                var updatedUser = await response.Content.ReadFromJsonAsync<UserDto>();
                if (updatedUser != null)
                {
                    // Déclencher l'événement de changement d'état d'authentification
                    AuthenticationStateChanged?.Invoke();
                }
                return updatedUser ?? new UserDto { IsAuthenticated = false };
            }
            
            return new UserDto { IsAuthenticated = false };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du profil: {ex.Message}");
            return new UserDto { IsAuthenticated = false };
        }
    }

    public async Task<UserDto> UpdateProfileAsync(UserDto user)
    {
        try
        {
            // Récupérer le token depuis le localStorage
            var token = await _localStorage.GetItemAsync<string>("authToken");
            
            if (string.IsNullOrEmpty(token))
            {
                Console.WriteLine("UpdateProfileAsync: Token vide, impossible de mettre à jour le profil");
                return new UserDto { IsAuthenticated = false };
            }
            
            // Supprimer les guillemets éventuels autour du token
            token = token.Trim('"');
            
            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            Console.WriteLine($"UpdateProfileAsync: En-tête d'autorisation configuré: {_httpClient.DefaultRequestHeaders.Authorization}");
            
            // Appeler l'API pour mettre à jour le profil utilisateur
            Console.WriteLine("UpdateProfileAsync: Envoi de la requête à api/users/profile");
            var response = await _httpClient.PutAsJsonAsync("api/users/profile", user);
            
            Console.WriteLine($"UpdateProfileAsync: Réponse du serveur: {response.StatusCode}");
            
            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("UpdateProfileAsync: Profil mis à jour avec succès");
                var updatedUser = await response.Content.ReadFromJsonAsync<UserDto>();
                if (updatedUser != null)
                {
                    updatedUser.IsAuthenticated = true;
                    return updatedUser;
                }
                return await GetCurrentUserAsync();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"UpdateProfileAsync: Erreur de la réponse: {errorContent}");
                return new UserDto { IsAuthenticated = false };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du profil utilisateur: {ex.Message}");
            Console.WriteLine($"StackTrace: {ex.StackTrace}");
            return new UserDto { IsAuthenticated = false };
        }
    }

    public async Task<bool> ChangePasswordAsync(ChangePasswordRequest request)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Appeler l'API pour changer le mot de passe
            var response = await _httpClient.PostAsJsonAsync("api/users/change-password", request);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du changement de mot de passe: {ex.Message}");
            return false;
        }
    }

    public async Task<List<RoleDto>> GetUserRolesAsync()
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return new List<RoleDto>();
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Récupérer l'utilisateur actuel pour obtenir son ID
            var user = await GetCurrentUserAsync();
            if (!user.IsAuthenticated)
            {
                return new List<RoleDto>();
            }

            // Appeler l'API pour récupérer les rôles de l'utilisateur
            var response = await _httpClient.GetAsync($"api/users/{user.Id}/roles");

            if (response.IsSuccessStatusCode)
            {
                var roles = await response.Content.ReadFromJsonAsync<List<RoleDto>>();
                return roles ?? new List<RoleDto>();
            }

            return new List<RoleDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des rôles de l'utilisateur: {ex.Message}");
            return new List<RoleDto>();
        }
    }

    public async Task<List<RoleDto>> GetAllRolesAsync()
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return new List<RoleDto>();
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Appeler l'API pour récupérer tous les rôles
            var response = await _httpClient.GetAsync("api/roles");

            if (response.IsSuccessStatusCode)
            {
                var roles = await response.Content.ReadFromJsonAsync<List<RoleDto>>();
                return roles ?? new List<RoleDto>();
            }

            return new List<RoleDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de tous les rôles: {ex.Message}");
            return new List<RoleDto>();
        }
    }
}
