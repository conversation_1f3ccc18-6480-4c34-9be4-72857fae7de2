@page "/sellers"
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using NafaPlace.AdminPortal.Models.Sellers
@using NafaPlace.AdminPortal.Services
@inject SellerService SellerService
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService

<h1 class="visually-hidden">Gestion des Vendeurs - NafaPlace Admin</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Vendeurs</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Vendeurs</li>
    </ol>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @errorMessage
            <button type="button" class="btn-close" @onclick="() => errorMessage = string.Empty"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @successMessage
            <button type="button" class="btn-close" @onclick="() => successMessage = string.Empty"></button>
        </div>
    }

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-shop me-1"></i>
                Liste des Vendeurs
            </div>
            <button class="btn btn-primary" @onclick="ShowCreateSellerModal">
                <i class="bi bi-plus-circle me-1"></i> Ajouter un Vendeur
            </button>
        </div>
        <div class="card-body">
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (sellers?.Any() == true)
            {
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Adresse</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var seller in sellers)
                            {
                                <tr>
                                    <td>@seller.Id</td>
                                    <td>@seller.Name</td>
                                    <td>@seller.Email</td>
                                    <td>@seller.PhoneNumber</td>
                                    <td>@seller.Address</td>
                                    <td>
                                        <span class="badge @(seller.IsActive ? "bg-success" : "bg-danger")">
                                            @(seller.IsActive ? "Actif" : "Inactif")
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" @onclick="() => EditSeller(seller)">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" @onclick="() => ToggleSellerStatus(seller)">
                                            <i class="bi @(seller.IsActive ? "bi-eye-slash" : "bi-eye")"></i>
                                        </button>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-4">
                    <i class="bi bi-shop" style="font-size: 3rem; color: #6c757d;"></i>
                    <p class="text-muted mt-2">Aucun vendeur trouvé</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal de création/modification de vendeur -->
@if (showCreateModal || showEditModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@(showCreateModal ? "Créer un nouveau vendeur" : "Modifier le vendeur")</h5>
                    <button type="button" class="btn-close" @onclick="CloseModals"></button>
                </div>
                <EditForm Model="currentSeller" OnValidSubmit="@(showCreateModal ? CreateSeller : UpdateSeller)">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom du vendeur *</label>
                                    <InputText @bind-Value="currentSeller.Name" class="form-control" />
                                    <ValidationMessage For="@(() => currentSeller.Name)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email *</label>
                                    <InputText @bind-Value="currentSeller.Email" class="form-control" />
                                    <ValidationMessage For="@(() => currentSeller.Email)" />
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <InputText @bind-Value="currentSeller.PhoneNumber" class="form-control" />
                                    <ValidationMessage For="@(() => currentSeller.PhoneNumber)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Statut</label>
                                    <InputCheckbox @bind-Value="currentSeller.IsActive" class="form-check-input" />
                                    <label class="form-check-label ms-2">Actif</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Adresse</label>
                            <InputTextArea @bind-Value="currentSeller.Address" class="form-control" rows="3" />
                            <ValidationMessage For="@(() => currentSeller.Address)" />
                        </div>
                        
                        @if (showCreateModal)
                        {
                            <hr />
                            <h6>Informations du compte utilisateur</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Nom d'utilisateur *</label>
                                        <InputText @bind-Value="currentSeller.Username" class="form-control" />
                                        <ValidationMessage For="@(() => currentSeller.Username)" />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Mot de passe *</label>
                                        <InputText @bind-Value="currentSeller.Password" type="password" class="form-control" />
                                        <ValidationMessage For="@(() => currentSeller.Password)" />
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModals">Annuler</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            @(showCreateModal ? "Créer" : "Modifier")
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

@code {
    private List<SellerDto>? sellers;
    private SellerDto currentSeller = new();
    private bool isLoading = true;
    private bool isSubmitting = false;
    private bool showCreateModal = false;
    private bool showEditModal = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadSellers();
    }

    private async Task LoadSellers()
    {
        try
        {
            isLoading = true;
            sellers = await SellerService.GetAllSellersAsync();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors du chargement des vendeurs: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ShowCreateSellerModal()
    {
        currentSeller = new SellerDto();
        showCreateModal = true;
    }

    private void EditSeller(SellerDto seller)
    {
        currentSeller = new SellerDto
        {
            Id = seller.Id,
            Name = seller.Name,
            Email = seller.Email,
            PhoneNumber = seller.PhoneNumber,
            Address = seller.Address,
            IsActive = seller.IsActive
        };
        showEditModal = true;
    }

    private async Task CreateSeller()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            var newSeller = await SellerService.CreateSellerAsync(currentSeller);
            successMessage = $"Vendeur {newSeller.Name} créé avec succès";
            showCreateModal = false;
            await LoadSellers();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la création du vendeur: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task UpdateSeller()
    {
        try
        {
            isSubmitting = true;
            errorMessage = string.Empty;

            var updatedSeller = await SellerService.UpdateSellerAsync(currentSeller);
            successMessage = $"Vendeur {updatedSeller.Name} modifié avec succès";
            showEditModal = false;
            await LoadSellers();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la modification du vendeur: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task ToggleSellerStatus(SellerDto seller)
    {
        try
        {
            seller.IsActive = !seller.IsActive;
            await SellerService.UpdateSellerAsync(seller);
            successMessage = $"Statut du vendeur {seller.Name} modifié avec succès";
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la modification du statut: {ex.Message}";
            seller.IsActive = !seller.IsActive; // Revert on error
        }
    }

    private void CloseModals()
    {
        showCreateModal = false;
        showEditModal = false;
        currentSeller = new();
    }
}
