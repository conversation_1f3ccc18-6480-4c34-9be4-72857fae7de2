using NafaPlace.AdminPortal.Models.Sellers;
using System.Text;
using System.Text.Json;

namespace NafaPlace.AdminPortal.Services
{
    public class SellerService
    {
        private readonly HttpClient _httpClient;
        private readonly HttpClient _identityHttpClient;
        private readonly ILogger<SellerService> _logger;

        public SellerService(HttpClient httpClient, IHttpClientFactory httpClientFactory, ILogger<SellerService> logger)
        {
            _httpClient = httpClient;
            _identityHttpClient = httpClientFactory.CreateClient("Identity");
            _logger = logger;
        }

        /// <summary>
        /// Récupère tous les vendeurs
        /// </summary>
        public async Task<List<SellerDto>> GetAllSellersAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/sellers");
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                var sellers = JsonSerializer.Deserialize<List<SellerDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return sellers ?? new List<SellerDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des vendeurs");
                throw;
            }
        }

        /// <summary>
        /// Crée un nouveau vendeur avec son compte utilisateur
        /// </summary>
        public async Task<SellerDto> CreateSellerAsync(SellerDto seller)
        {
            try
            {
                // 1. Créer d'abord l'utilisateur dans le service Identity
                var userRequest = new
                {
                    Email = seller.Email,
                    Username = seller.Username,
                    Password = seller.Password,
                    FirstName = seller.Name.Split(' ').FirstOrDefault() ?? seller.Name,
                    LastName = seller.Name.Split(' ').Skip(1).FirstOrDefault() ?? ""
                };

                var userJson = JsonSerializer.Serialize(userRequest);
                var userContent = new StringContent(userJson, Encoding.UTF8, "application/json");

                _logger.LogInformation($"Création de l'utilisateur vendeur: {seller.Email}");
                
                var userResponse = await _identityHttpClient.PostAsync("/api/auth/register-seller", userContent);
                
                if (!userResponse.IsSuccessStatusCode)
                {
                    var errorContent = await userResponse.Content.ReadAsStringAsync();
                    throw new Exception($"Erreur lors de la création de l'utilisateur: {errorContent}");
                }

                var userResponseJson = await userResponse.Content.ReadAsStringAsync();
                var userResult = JsonSerializer.Deserialize<JsonElement>(userResponseJson);
                
                // Extraire l'ID utilisateur de la réponse
                var userId = userResult.GetProperty("user").GetProperty("id").GetInt32().ToString();

                _logger.LogInformation($"Utilisateur créé avec l'ID: {userId}");

                // 2. Le vendeur devrait déjà être créé automatiquement par le service Identity
                // Mais on va vérifier et le récupérer
                await Task.Delay(1000); // Attendre un peu pour la synchronisation

                var sellerResponse = await _httpClient.GetAsync($"/api/v1/sellers/by-user/{userId}");
                
                if (sellerResponse.IsSuccessStatusCode)
                {
                    var sellerJson = await sellerResponse.Content.ReadAsStringAsync();
                    var createdSeller = JsonSerializer.Deserialize<SellerDto>(sellerJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return createdSeller ?? seller;
                }
                else
                {
                    // Si le vendeur n'a pas été créé automatiquement, le créer manuellement
                    _logger.LogWarning($"Vendeur non trouvé pour l'utilisateur {userId}, création manuelle");
                    
                    var sellerRequest = new
                    {
                        Name = seller.Name,
                        Email = seller.Email,
                        PhoneNumber = seller.PhoneNumber,
                        Address = seller.Address,
                        IsActive = seller.IsActive,
                        UserId = userId
                    };

                    var sellerRequestJson = JsonSerializer.Serialize(sellerRequest);
                    var sellerContent = new StringContent(sellerRequestJson, Encoding.UTF8, "application/json");

                    var createSellerResponse = await _httpClient.PostAsync("/api/v1/sellers", sellerContent);
                    createSellerResponse.EnsureSuccessStatusCode();

                    var createdSellerJson = await createSellerResponse.Content.ReadAsStringAsync();
                    var createdSeller = JsonSerializer.Deserialize<SellerDto>(createdSellerJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return createdSeller ?? seller;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création du vendeur");
                throw;
            }
        }

        /// <summary>
        /// Met à jour un vendeur existant
        /// </summary>
        public async Task<SellerDto> UpdateSellerAsync(SellerDto seller)
        {
            try
            {
                var json = JsonSerializer.Serialize(seller);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"/api/v1/sellers/{seller.Id}", content);
                response.EnsureSuccessStatusCode();

                var responseJson = await response.Content.ReadAsStringAsync();
                var updatedSeller = JsonSerializer.Deserialize<SellerDto>(responseJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return updatedSeller ?? seller;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la mise à jour du vendeur");
                throw;
            }
        }

        /// <summary>
        /// Récupère un vendeur par son ID
        /// </summary>
        public async Task<SellerDto?> GetSellerByIdAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/sellers/{id}");
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }

                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                var seller = JsonSerializer.Deserialize<SellerDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return seller;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération du vendeur");
                throw;
            }
        }
    }
}
