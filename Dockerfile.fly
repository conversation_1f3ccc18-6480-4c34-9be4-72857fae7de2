# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier la solution et tous les fichiers de projet
COPY . .

# Restaurer les dépendances
RUN dotnet restore "NafaPlace.sln"

# Build la solution complète
RUN dotnet build "NafaPlace.sln" -c Release

# Publish all microservices
FROM build AS publish-identity
WORKDIR "/src/src/Services/Identity/NafaPlace.Identity.API"
RUN dotnet publish "NafaPlace.Identity.API.csproj" -c Release -o /app/identity /p:UseAppHost=false

FROM build AS publish-catalog
WORKDIR "/src/src/Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API"
RUN dotnet publish "NafaPlace.Catalog.API.csproj" -c Release -o /app/catalog /p:UseAppHost=false

FROM build AS publish-cart
WORKDIR "/src/src/Services/Cart/NafaPlace.Cart.API"
RUN dotnet publish "NafaPlace.Cart.API.csproj" -c Release -o /app/cart /p:UseAppHost=false

FROM build AS publish-order
WORKDIR "/src/src/Services/Order/NafaPlace.Order.API"
RUN dotnet publish "NafaPlace.Order.API.csproj" -c Release -o /app/order /p:UseAppHost=false

FROM build AS publish-payment
WORKDIR "/src/src/Services/Payment/NafaPlace.Payment.API"
RUN dotnet publish "NafaPlace.Payment.API.csproj" -c Release -o /app/payment /p:UseAppHost=false

FROM build AS publish-gateway
WORKDIR "/src/src/ApiGateways/Web/NafaPlace.ApiGateway"
RUN dotnet publish "NafaPlace.ApiGateway.csproj" -c Release -o /app/gateway /p:UseAppHost=false

FROM build AS publish-web
WORKDIR "/src/src/Web/NafaPlace.Web"
RUN dotnet publish "NafaPlace.Web.csproj" -c Release -o /app/web /p:UseAppHost=false

FROM build AS publish-admin
WORKDIR "/src/src/Web/AdminPortal/NafaPlace.AdminPortal"
RUN dotnet publish "NafaPlace.AdminPortal.csproj" -c Release -o /app/admin /p:UseAppHost=false

FROM build AS publish-seller
WORKDIR "/src/src/Web/SellerPortal/NafaPlace.SellerPortal"
RUN dotnet publish "NafaPlace.SellerPortal.csproj" -c Release -o /app/seller /p:UseAppHost=false

# Final stage - Multi-service container with supervisor
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app

# Install supervisor and nginx for managing multiple processes and serving static files
RUN apt-get update && apt-get install -y supervisor nginx && rm -rf /var/lib/apt/lists/*

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Conakry
ENV LANG=fr_GN.UTF-8
ENV LANGUAGE=fr_GN.UTF-8
ENV LC_ALL=fr_GN.UTF-8

# Copy all published applications
COPY --from=publish-identity /app/identity ./identity/
COPY --from=publish-catalog /app/catalog ./catalog/
COPY --from=publish-cart /app/cart ./cart/
COPY --from=publish-order /app/order ./order/
COPY --from=publish-payment /app/payment ./payment/
COPY --from=publish-gateway /app/gateway ./gateway
COPY --from=publish-web /app/web ./web
COPY --from=publish-admin /app/admin ./admin
COPY --from=publish-seller /app/seller ./seller/

# Create supervisor configuration
RUN mkdir -p /etc/supervisor/conf.d
COPY <<EOF /etc/supervisor/conf.d/supervisord.conf
[supervisord]
nodaemon=true
user=root

[program:identity-api]
command=dotnet /app/identity/NafaPlace.Identity.API.dll
directory=/app/identity
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5001",ASPNETCORE_ENVIRONMENT="Staging",DATABASE_URL="%(ENV_DATABASE_URL)s",JWT_SECRET="%(ENV_JWT_SECRET)s"

[program:catalog-api]
command=dotnet /app/catalog/NafaPlace.Catalog.API.dll
directory=/app/catalog
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5002",ASPNETCORE_ENVIRONMENT="Staging",DATABASE_URL="%(ENV_DATABASE_URL)s",CLOUDINARY_CLOUD_NAME="%(ENV_CLOUDINARY_CLOUD_NAME)s",CLOUDINARY_API_KEY="%(ENV_CLOUDINARY_API_KEY)s",CLOUDINARY_API_SECRET="%(ENV_CLOUDINARY_API_SECRET)s"

[program:cart-api]
command=dotnet /app/cart/NafaPlace.Cart.API.dll
directory=/app/cart
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5003",ASPNETCORE_ENVIRONMENT="Staging",REDIS_URL="%(ENV_REDIS_URL)s"

[program:order-api]
command=dotnet /app/order/NafaPlace.Order.API.dll
directory=/app/order
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5004",ASPNETCORE_ENVIRONMENT="Staging",DATABASE_URL="%(ENV_DATABASE_URL)s"

[program:payment-api]
command=dotnet /app/payment/NafaPlace.Payment.API.dll
directory=/app/payment
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5005",ASPNETCORE_ENVIRONMENT="Staging",STRIPE_PUBLISHABLE_KEY="%(ENV_STRIPE_PUBLISHABLE_KEY)s",STRIPE_SECRET_KEY="%(ENV_STRIPE_SECRET_KEY)s"

[program:api-gateway]
command=dotnet /app/gateway/NafaPlace.ApiGateway.dll
directory=/app/gateway
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:8080",ASPNETCORE_ENVIRONMENT="Staging"

[program:nginx-web]
command=nginx -g "daemon off;" -c /etc/nginx/web.conf
autostart=true
autorestart=true

[program:nginx-admin]
command=nginx -g "daemon off;" -c /etc/nginx/admin.conf
autostart=true
autorestart=true

[program:nginx-seller]
command=nginx -g "daemon off;" -c /etc/nginx/seller.conf
autostart=true
autorestart=true
EOF

# Create nginx configurations for Blazor WebAssembly apps
COPY <<EOF /etc/nginx/web.conf
events {
    worker_connections 1024;
}
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 8081;
        server_name _;
        root /app/web/wwwroot;
        index index.html;

        location / {
            try_files \$uri \$uri/ /index.html;
        }

        location /_framework/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
EOF

COPY <<EOF /etc/nginx/admin.conf
events {
    worker_connections 1024;
}
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 8082;
        server_name _;
        root /app/admin/wwwroot;
        index index.html;

        location / {
            try_files \$uri \$uri/ /index.html;
        }

        location /_framework/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
EOF

COPY <<EOF /etc/nginx/seller.conf
events {
    worker_connections 1024;
}
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 8083;
        server_name _;
        root /app/seller/wwwroot;
        index index.html;

        location / {
            try_files \$uri \$uri/ /index.html;
        }

        location /_framework/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
EOF

EXPOSE 8080 8081 8082 8083

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
