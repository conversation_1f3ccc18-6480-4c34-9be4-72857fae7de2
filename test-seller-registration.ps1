# Test de création de compte vendeur
Write-Host "=== Test de création de compte vendeur ===" -ForegroundColor Green

# Données pour le nouveau compte vendeur
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$newSellerData = @{
    firstName = "Test"
    lastName = "Seller"
    email = "testseller$<EMAIL>"
    password = "Seller123!"
    confirmPassword = "Seller123!"
    phoneNumber = "+224123456789"
    companyName = "Test Company $timestamp"
    businessType = "Retail"
    address = "123 Test Street, Conakry"
}

Write-Host "Tentative de création de compte pour: $($newSellerData.email)" -ForegroundColor Yellow

try {
    # Test de création de compte
    $registerResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/register-seller" `
        -Method POST `
        -ContentType "application/json" `
        -Body ($newSellerData | ConvertTo-Json) `
        -ErrorAction Stop

    Write-Host "✅ Création de compte réussie!" -ForegroundColor Green
    Write-Host "Réponse: $($registerResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan

    # Test de connexion avec le nouveau compte
    Write-Host "`n=== Test de connexion avec le nouveau compte ===" -ForegroundColor Green
    
    $loginData = @{
        email = $newSellerData.email
        password = $newSellerData.password
    }

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" `
        -Method POST `
        -ContentType "application/json" `
        -Body ($loginData | ConvertTo-Json) `
        -ErrorAction Stop

    Write-Host "✅ Connexion réussie!" -ForegroundColor Green
    Write-Host "Token reçu: $($loginResponse.token.Substring(0, 50))..." -ForegroundColor Cyan

} catch {
    Write-Host "❌ Erreur lors du test:" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Réponse du serveur: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`n=== Test terminé ===" -ForegroundColor Green
