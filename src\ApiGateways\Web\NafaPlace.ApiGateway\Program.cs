using Ocelot.DependencyInjection;
using Ocelot.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add configuration for Ocelot
var ocelotConfigFile = builder.Environment.IsStaging() ? "ocelot.staging.json" : "ocelot.json";
builder.Configuration.AddJsonFile(ocelotConfigFile, optional: false, reloadOnChange: true);

// Add services to the container
builder.Services.AddOcelot();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add Swagger for API Gateway
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "NafaPlace API Gateway",
        Version = "v1",
        Description = "API Gateway pour la plateforme e-commerce NafaPlace"
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "NafaPlace API Gateway V1");
        c.RoutePrefix = "swagger";
    });
}

// Désactiver HTTPS redirection pour fly.io en staging
if (!app.Environment.IsStaging())
{
    app.UseHttpsRedirection();
}
app.UseCors("AllowAll");

// Middleware personnalisé pour intercepter les requêtes avant Ocelot
app.Use(async (context, next) =>
{
    if (context.Request.Path == "/health")
    {
        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(new {
            status = "Healthy",
            timestamp = DateTime.UtcNow,
            environment = "Staging",
            version = "1.0.0"
        }));
        return;
    }

    if (context.Request.Path == "/services-status" && context.Request.Method == "GET")
    {
        context.Response.ContentType = "application/json";

        // Test connectivity to each microservice
        var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromSeconds(5);

        var servicesStatus = new Dictionary<string, object>();

        var services = new[]
        {
            new { Name = "identity", Url = "http://localhost:5155/api/UserRoleFix/health", Port = 5155 },
            new { Name = "catalog", Url = "http://localhost:5243/api/v1/categories/health", Port = 5243 },
            new { Name = "cart", Url = "http://localhost:5003/weatherforecast", Port = 5003 },
            new { Name = "order", Url = "http://localhost:5004/weatherforecast", Port = 5004 },
            new { Name = "payment", Url = "http://localhost:5005/weatherforecast", Port = 5005 }
        };

        foreach (var service in services)
        {
            try
            {
                var response = await httpClient.GetAsync(service.Url);
                servicesStatus[service.Name] = new {
                    status = response.IsSuccessStatusCode ? "Healthy" : "Unhealthy",
                    port = service.Port,
                    statusCode = (int)response.StatusCode,
                    url = service.Url
                };
            }
            catch (Exception ex)
            {
                servicesStatus[service.Name] = new {
                    status = "Error",
                    port = service.Port,
                    error = ex.Message,
                    url = service.Url
                };
            }
        }

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(new {
            message = "NafaPlace Services Status Check",
            timestamp = DateTime.UtcNow,
            environment = app.Environment.EnvironmentName,
            services = servicesStatus
        }));
        return;
    }

    if (context.Request.Path == "/" && context.Request.Method == "GET")
    {
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(new {
            message = "NafaPlace API Gateway - Test Deployment",
            status = "Running",
            timestamp = DateTime.UtcNow,
            environment = app.Environment.EnvironmentName,
            version = "1.0.0",
            endpoints = new[] { "/health", "/services-status", "/swagger", "/api/identity/*", "/api/catalog/*", "/api/cart/*", "/api/orders/*", "/api/payments/*" },
            database_status = "Checking...",
            services_status = new {
                identity = "Pending deployment",
                catalog = "Pending deployment",
                cart = "Pending deployment",
                orders = "Pending deployment",
                payments = "Pending deployment"
            }
        }));
        return;
    }

    await next();
});

// Use Ocelot middleware
await app.UseOcelot();

app.Run();
