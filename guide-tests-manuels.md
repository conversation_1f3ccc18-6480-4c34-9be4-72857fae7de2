# Guide de Tests Manuels - NafaPlace

## 🎯 Comptes de Test Créés

### Utilisateur Standard
- **Email**: <EMAIL>
- **Username**: testuser1732
- **Mot de passe**: User123!

### Vendeur
- **Email**: <EMAIL>
- **Username**: testseller1732
- **Mot de passe**: Seller123!

### Administrateur
- **Username**: admin
- **Mot de passe**: Admin123!
- ⚠️ **Important**: Utiliser le USERNAME, pas l'email pour la connexion

## 🌐 Portails Web

### Main Web Portal - http://localhost:8080
**Tests à effectuer:**
1. **Page d'accueil**
   - Vérifier l'affichage des produits
   - Tester la recherche
   - Vérifier les catégories

2. **Inscription/Connexion**
   - Créer un nouveau compte utilisateur
   - Se connecter avec: `testuser1732` / `User123!`
   - Vérifier le profil utilisateur

3. **Fonctionnalités E-commerce**
   - Consulter les détails d'un produit
   - Ajouter au panier (si disponible)
   - Tester le processus de commande

### Admin Portal - http://localhost:8081
**Tests à effectuer:**
1. **Connexion Admin**
   - Se connecter avec: `admin` / `Admin123!`
   - ⚠️ Utiliser le USERNAME, pas l'email

2. **Gestion des Utilisateurs**
   - Accéder à la liste des utilisateurs
   - Vérifier les nouveaux comptes créés
   - Tester la modification des rôles

3. **Gestion des Rôles**
   - Consulter la liste des rôles
   - Vérifier les permissions

4. **Gestion du Catalog**
   - Consulter les produits
   - Consulter les catégories
   - Créer/modifier des catégories (admin uniquement)

### Seller Portal - http://localhost:8082
**Tests à effectuer:**
1. **Connexion Vendeur**
   - Se connecter avec: `testseller1732` / `Seller123!`
   - ⚠️ Utiliser le USERNAME, pas l'email

2. **Gestion des Produits**
   - Créer un nouveau produit
   - Modifier un produit existant
   - Gérer le stock

3. **Tableau de Bord**
   - Consulter les statistiques
   - Vérifier les commandes (si disponible)

## 🔧 Problèmes Identifiés

### ❌ Connexion par Email
- **Problème**: Toutes les connexions par email retournent une erreur 400
- **Solution temporaire**: Utiliser le USERNAME au lieu de l'email
- **Impact**: Affecte tous les portails

### ✅ Fonctionnalités Opérationnelles
- Création de comptes utilisateur et vendeur
- Connexion par username
- API Catalog (produits et catégories)
- Récupération du profil utilisateur

## 📋 Checklist de Tests

### Tests Automatisés Réalisés ✅
- [x] Création compte utilisateur
- [x] Création compte vendeur  
- [x] Connexion admin par username
- [x] Récupération produits/catégories
- [x] Test des endpoints API

### Tests Manuels à Effectuer
- [ ] Navigation complète Main Web Portal
- [ ] Processus d'achat complet
- [ ] Gestion utilisateurs Admin Portal
- [ ] Création produits Seller Portal
- [ ] Test responsive design
- [ ] Test fonctionnalités panier/commande

## 🚀 Instructions de Test

1. **Ouvrir les trois portails** dans des onglets séparés
2. **Commencer par l'Admin Portal** pour vérifier la gestion
3. **Tester le Seller Portal** pour la création de produits
4. **Finir par le Main Web** pour l'expérience utilisateur final

## 🔍 Points d'Attention

- **Connexions**: Toujours utiliser le USERNAME
- **Rôles**: Vérifier que les permissions sont correctes
- **Navigation**: Tester tous les menus et liens
- **Responsive**: Tester sur mobile/tablette
- **Erreurs**: Noter toute erreur ou comportement inattendu

## 📞 Support

Si vous rencontrez des problèmes:
1. Vérifier les logs Docker: `docker logs nafaplace-identity-api`
2. Redémarrer les services si nécessaire: `docker compose restart`
3. Utiliser les comptes de test fournis ci-dessus
