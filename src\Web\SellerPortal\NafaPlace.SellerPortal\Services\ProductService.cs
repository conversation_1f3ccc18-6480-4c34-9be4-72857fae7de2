﻿// Recompile
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using NafaPlace.SellerPortal.Models;
using NafaPlace.Catalog.Domain.Models;

// Classe pour correspondre Ã  la structure de rÃ©ponse de l'API
public class ApiPagedResponse
{
    public List<Product> Items { get; set; } = new List<Product>();
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
}

namespace NafaPlace.SellerPortal.Services
{
    public class ProductService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ProductService> _logger;
        private readonly string _apiBaseUrl;

        // Cache pour les produits et autres donnÃ©es
        private static readonly ConcurrentDictionary<string, object> _cache = new ConcurrentDictionary<string, object>();
        private static readonly ConcurrentDictionary<int, Product> _productCache = new ConcurrentDictionary<int, Product>();
        private static readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(5);
        private static DateTime _lastCacheRefresh = DateTime.MinValue;
        
        // Image par dÃ©faut lÃ©gÃ¨re (1x1 pixel transparent)
        private const string DefaultImageBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";

        public ProductService(IHttpClientFactory httpClientFactory, ILogger<ProductService> logger, IConfiguration configuration)
        {
            _httpClient = httpClientFactory.CreateClient("CatalogApi");
            _logger = logger;
            _apiBaseUrl = configuration.GetValue<string>("ApiSettings:CatalogApiUrl");
            if (!string.IsNullOrEmpty(_apiBaseUrl))
            {
                _httpClient.BaseAddress = new Uri(_apiBaseUrl);
            }
        }

        public async Task<ProductPagedResponse> GetProductsAsync(int sellerId, int page, int pageSize)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/products?sellerId={sellerId}&page={page}&pageSize={pageSize}");
                response.EnsureSuccessStatusCode();

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                // L'API retourne une structure PagedResultDto, nous devons la convertir
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiPagedResponse>(options);

                if (apiResponse != null)
                {
                    return new ProductPagedResponse
                    {
                        Products = apiResponse.Items ?? new List<Product>(),
                        TotalPages = apiResponse.TotalPages,
                        TotalCount = apiResponse.TotalCount
                    };
                }

                return new ProductPagedResponse();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration des produits: {ex.Message}");
                Console.WriteLine($"DÃ©tails de l'erreur: {ex.StackTrace}");
                return new ProductPagedResponse();
            }
        }

        public async Task<List<Category>> GetCategoriesAsync()
        {
            // VÃ©rifier si les catÃ©gories sont en cache
            string cacheKey = "all_categories";
            if (_cache.TryGetValue(cacheKey, out var cachedData) && 
                DateTime.Now.Subtract(_lastCacheRefresh) < _cacheExpiration)
            {
                _logger?.LogInformation("RÃ©cupÃ©ration des catÃ©gories depuis le cache");
                return (List<Category>)cachedData;
            }

            _logger?.LogInformation("RÃ©cupÃ©ration des catÃ©gories depuis l'API");
            try
            {
                // Utiliser un dÃ©lai d'attente court pour la requÃªte
                var timeoutTask = Task.Delay(5000); // 5 secondes
                var responseTask = _httpClient.GetFromJsonAsync<List<Category>>("/api/v1/categories");
                
                var completedTask = await Task.WhenAny(responseTask, timeoutTask);
                if (completedTask == timeoutTask)
                {
                    _logger?.LogWarning("DÃ©lai d'attente dÃ©passÃ© lors de la rÃ©cupÃ©ration des catÃ©gories");
                    // Si des donnÃ©es sont en cache, les retourner mÃªme si elles sont expirÃ©es
                    if (cachedData != null)
                    {
                        return (List<Category>)cachedData;
                    }
                    // Sinon, retourner des donnÃ©es de test
                    return GetMockCategories();
                }
                
                var categories = await responseTask;
                if (categories != null)
                {
                    // Mettre Ã  jour le cache
                    _cache[cacheKey] = categories;
                    _lastCacheRefresh = DateTime.Now;
                    return categories;
                }
                else
                {
                    // Si des donnÃ©es sont en cache, les retourner mÃªme si elles sont expirÃ©es
                    if (cachedData != null)
                    {
                        return (List<Category>)cachedData;
                    }
                    // Sinon, retourner des donnÃ©es de test
                    return GetMockCategories();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Erreur lors de la rÃ©cupÃ©ration des catÃ©gories");
                // Si des donnÃ©es sont en cache, les retourner mÃªme si elles sont expirÃ©es
                if (cachedData != null)
                {
                    return (List<Category>)cachedData;
                }
                // Sinon, retourner des donnÃ©es de test
                return GetMockCategories();
            }
        }

        public async Task<Product> GetProductAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<Product>($"/api/v1/products/{id}");
                return response ?? new Product { Name = "", Description = "", Currency = "GNF" };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration du produit: {ex.Message}");
                // En cas d'erreur, retourner un produit de test
                return GetMockProducts().Find(p => p.Id == id) ?? new Product { Name = "", Description = "", Currency = "GNF" };
            }
        }

        public async Task<Product> GetProductByIdAsync(int productId)
        {
            // VÃ©rifier si le produit est en cache
            if (_productCache.TryGetValue(productId, out var cachedProduct))
            {
                _logger?.LogInformation($"RÃ©cupÃ©ration du produit {productId} depuis le cache");
                return cachedProduct;
            }

            _logger?.LogInformation($"RÃ©cupÃ©ration du produit {productId} depuis l'API");
            try
            {
                // Utiliser un dÃ©lai d'attente court pour la requÃªte
                var timeoutTask = Task.Delay(5000); // 5 secondes
                var responseTask = _httpClient.GetAsync($"/api/v1/products/{productId}");
                
                var completedTask = await Task.WhenAny(responseTask, timeoutTask);
                if (completedTask == timeoutTask)
                {
                    _logger?.LogWarning($"DÃ©lai d'attente dÃ©passÃ© lors de la rÃ©cupÃ©ration du produit {productId}");
                    return null;
                }
                
                var response = await responseTask;
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var product = JsonSerializer.Deserialize<Product>(jsonString, options) ?? new Product { Name = "", Description = "", Currency = "GNF" };
                    
                    // S'assurer que les images sont correctement initialisÃ©es
                    if (product != null && product.Images == null)
                    {
                        product.Images = new List<ProductImage>();
                    }
                    
                    // VÃ©rifier si les images ont le bon format
                    if (product?.Images != null)
                    {
                        foreach (var image in product.Images)
                        {
                            // S'assurer que l'ImageUrl est correctement dÃ©fini
                            if (string.IsNullOrEmpty(image.ImageUrl) && image.GetType().GetProperty("imageUrl") != null)
                            {
                                var imageUrlProp = image.GetType().GetProperty("imageUrl");
                                if (imageUrlProp != null)
                                {
                                    image.ImageUrl = imageUrlProp.GetValue(image)?.ToString() ?? string.Empty;
                                }
                            }
                        }
                    }
                    
                    Console.WriteLine($"Produit rÃ©cupÃ©rÃ© avec {product?.Images?.Count ?? 0} images");
                    return product;
                }
                else
                {
                    Console.WriteLine($"Erreur lors de la rÃ©cupÃ©ration du produit {productId}: {response.StatusCode}");
                    return new Product { Name = "", Description = "", Currency = "GNF" };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception lors de la rÃ©cupÃ©ration du produit {productId}: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }
                return new Product { Name = "", Description = "", Currency = "GNF" };
            }
        }

        public string GetImageUrl(ProductImage image, bool thumbnail = false)
        {
            if (image == null)
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            var url = thumbnail ? image.ThumbnailUrl : image.ImageUrl;

            if (string.IsNullOrEmpty(url))
            {
                // Fallback to the other url if one is empty
                url = thumbnail ? image.ImageUrl : image.ThumbnailUrl;
            }

            if (string.IsNullOrEmpty(url))
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            if (url.StartsWith("http://") || url.StartsWith("https://") || url.StartsWith("data:"))
            {
                // Convertir les URLs Azurite internes en URLs accessibles depuis le navigateur
                if (url.Contains("azurite:10000"))
                {
                    return url.Replace("http://azurite:10000", "http://localhost:10000");
                }
                return url;
            }

            return _apiBaseUrl + (url.StartsWith("/") ? url : $"/{url}");
        }

        public async Task<Product> CreateProductAsync(Product product)
        {
            try
            {
                var createRequest = new
                {
                    Name = product.Name,
                    Description = product.Description,
                    Price = product.Price,
                    Currency = product.Currency ?? "XOF",
                    StockQuantity = product.StockQuantity,
                    CategoryId = product.CategoryId,
                    SellerId = product.SellerId,
                    Brand = product.Brand ?? string.Empty,
                    Model = product.Model ?? string.Empty,
                    Weight = product.Weight,
                    Dimensions = product.Dimensions ?? "0x0x0",
                    IsActive = product.IsActive,
                    IsFeatured = product.IsFeatured,
                    Images = new List<object>(),
                    Variants = new List<object>(),
                    Attributes = new List<object>()
                };

                var jsonString = JsonSerializer.Serialize(createRequest);
                var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync("/api/v1/products", content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Erreur lors de la crÃ©ation du produit: {response.StatusCode}, DÃ©tails: {errorContent}");
                    return null;
                }

                var createdProduct = await response.Content.ReadFromJsonAsync<Product>();

                if (createdProduct != null && product.Images != null && product.Images.Any())
                {
                    await UploadImagesAsync(createdProduct.Id, product.Images);
                }

                return createdProduct;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception lors de la crÃ©ation du produit: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }
                return null;
            }
        }

        public async Task<Product> UpdateProductAsync(Product product)
        {
            try
            {
                var updateRequest = new
                {
                    Name = product.Name,
                    Description = product.Description,
                    Price = product.Price,
                    Currency = product.Currency ?? "XOF",
                    StockQuantity = product.StockQuantity,
                    CategoryId = product.CategoryId,
                    Brand = product.Brand ?? string.Empty,
                    Model = product.Model ?? string.Empty,
                    Weight = product.Weight,
                    Dimensions = product.Dimensions ?? "0x0x0",
                    IsActive = product.IsActive,
                    IsFeatured = product.IsFeatured,
                    Variants = product.Variants.Select(v => new { v.Id, v.Name, v.Price, v.StockQuantity, v.Sku }).ToList(),
                    Images = new List<object>()
                };

                var content = new StringContent(JsonSerializer.Serialize(updateRequest), Encoding.UTF8, "application/json");
                var response = await _httpClient.PutAsync($"/api/v1/products/{product.Id}", content);

                if (response.IsSuccessStatusCode)
                {
                    var updatedProduct = await response.Content.ReadFromJsonAsync<Product>();

                    // GÃ©rer le tÃ©lÃ©chargement des images
                    if (updatedProduct != null && product.Images != null)
                    {
                        var newImages = product.Images.Where(i => i.Id == 0).ToList();
                        if (newImages.Any())
                        {
                            await UploadImagesAsync(updatedProduct.Id, newImages);
                        }
                    }

                    return updatedProduct;
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Erreur lors de la mise Ã  jour du produit: {response.StatusCode}, {errorContent}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception lors de la mise Ã  jour du produit: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UploadImagesAsync(int productId, List<ProductImage> newImages)
        {
            if (newImages == null || !newImages.Any())
            {
                return true;
            }

            try
            {
                // Convertir les images au format attendu par l'API
                var imageUploadRequests = newImages.Select(image => new
                {
                    ProductId = productId,
                    Image = ExtractBase64FromDataUrl(image.ImageUrl), // Extraire seulement la partie base64
                    IsMain = image.IsMain
                }).ToList();

                var imageContent = new StringContent(JsonSerializer.Serialize(imageUploadRequests), Encoding.UTF8, "application/json");
                var imageResponse = await _httpClient.PostAsync($"/api/v1/products/{productId}/images/bulk", imageContent);

                if (!imageResponse.IsSuccessStatusCode)
                {
                    var error = await imageResponse.Content.ReadAsStringAsync();
                    Console.WriteLine($"Ã‰chec du tÃ©lÃ©chargement des images pour le produit ID {productId}. Statut: {imageResponse.StatusCode}. Erreur: {error}");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception lors du tÃ©lÃ©chargement des images: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/v1/products/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la suppression du produit: {ex.Message}");
                return false;
            }
        }

        private string ExtractBase64FromDataUrl(string dataUrl)
        {
            // Extraire la partie base64 d'une data URL (ex: "data:image/png;base64,iVBORw0KGgo...")
            if (string.IsNullOrEmpty(dataUrl))
                return string.Empty;

            if (dataUrl.Contains(","))
            {
                return dataUrl.Split(',')[1]; // Prendre la partie aprÃ¨s la virgule
            }

            return dataUrl; // Si ce n'est pas une data URL, retourner tel quel
        }

        // MÃ©thodes pour gÃ©nÃ©rer des donnÃ©es de test
        private List<Product> GetMockProducts()
        {
            return new List<Product>
            {
                new Product
                {
                    Id = 1,
                    Name = "Smartphone XYZ",
                    Description = "Un smartphone de derniÃ¨re gÃ©nÃ©ration",
                    Price = 299.99m,
                    Currency = "EUR",
                    StockQuantity = 50,
                    CategoryId = 1,
                    SellerId = 1,
                    Brand = "XYZ",
                    Model = "S10",
                    Images = new List<ProductImage>
                    {
                        new ProductImage { Id = 1, ProductId = 1, ImageUrl = "data:image/png;base64,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", IsMain = true }
                    }
                },
                new Product
                {
                    Id = 2,
                    Name = "Laptop ABC",
                    Description = "Un laptop puissant pour les professionnels",
                    Price = 899.99m,
                    Currency = "EUR",
                    StockQuantity = 20,
                    CategoryId = 1,
                    SellerId = 1,
                    Brand = "ABC",
                    Model = "Pro 15",
                    Images = new List<ProductImage>
                    {
                        new ProductImage { Id = 2, ProductId = 2, ImageUrl = "data:image/png;base64,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", IsMain = true }
                    }
                }
            };
        }

        private List<Category> GetMockCategories()
        {
            return new List<Category>
            {
                new Category { Id = 1, Name = "Ã‰lectronique", Description = "Produits Ã©lectroniques", ImageUrl = "" },
                new Category { Id = 2, Name = "VÃªtements", Description = "VÃªtements et accessoires", ImageUrl = "" },
                new Category { Id = 3, Name = "Maison", Description = "Articles pour la maison", ImageUrl = "" }
            };
        }
        

    }
}

