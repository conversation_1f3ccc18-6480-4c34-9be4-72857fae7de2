# Script de validation du déploiement NafaPlace
# Utilisation: .\scripts\validate-deployment.ps1

Write-Host "🔍 Validation du déploiement NafaPlace" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

$allTestsPassed = $true

# Test 1: Vérifier que tous les conteneurs sont en cours d'exécution
Write-Host "`n📦 Test 1: État des conteneurs" -ForegroundColor Yellow
$containers = docker-compose ps --services
$runningContainers = docker-compose ps --services --filter "status=running"

if ($containers.Count -eq $runningContainers.Count) {
    Write-Host "✅ Tous les conteneurs sont en cours d'exécution" -ForegroundColor Green
} else {
    Write-Host "❌ Certains conteneurs ne sont pas en cours d'exécution" -ForegroundColor Red
    $allTestsPassed = $false
}

# Test 2: Vérifier les endpoints de santé
Write-Host "`n🏥 Test 2: Endpoints de santé" -ForegroundColor Yellow
$healthEndpoints = @(
    @{Name="API Gateway"; Url="http://localhost:5000/health"},
    @{Name="Identity API"; Url="http://localhost:5155/health"},
    @{Name="Catalog API"; Url="http://localhost:5243/health"},
    @{Name="Cart API"; Url="http://localhost:5003/health"},
    @{Name="Order API"; Url="http://localhost:5004/health"},
    @{Name="Payment API"; Url="http://localhost:5005/health"},
    @{Name="Reviews API"; Url="http://localhost:5006/health"},
    @{Name="Notifications API"; Url="http://localhost:5007/health"}
)

foreach ($endpoint in $healthEndpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint.Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($endpoint.Name) - Healthy" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($endpoint.Name) - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($endpoint.Name) - Non accessible" -ForegroundColor Red
        $allTestsPassed = $false
    }
}

# Test 3: Vérifier les bases de données
Write-Host "`n💾 Test 3: Connexions aux bases de données" -ForegroundColor Yellow
$databases = @(
    @{Name="Catalog DB"; Container="nafaplace-catalog-db"; Port="5432"},
    @{Name="Identity DB"; Container="nafaplace-identity-db"; Port="5433"},
    @{Name="Order DB"; Container="nafaplace-order-db"; Port="5434"},
    @{Name="Reviews DB"; Container="nafaplace-reviews-db"; Port="5435"},
    @{Name="Notifications DB"; Container="nafaplace-notifications-db"; Port="5436"}
)

foreach ($db in $databases) {
    try {
        $result = docker exec $db.Container pg_isready -U postgres
        if ($result -match "accepting connections") {
            Write-Host "✅ $($db.Name) - Connectée" -ForegroundColor Green
        } else {
            Write-Host "❌ $($db.Name) - Non connectée" -ForegroundColor Red
            $allTestsPassed = $false
        }
    } catch {
        Write-Host "❌ $($db.Name) - Erreur de connexion" -ForegroundColor Red
        $allTestsPassed = $false
    }
}

# Test 4: Vérifier Redis
Write-Host "`n🔴 Test 4: Redis" -ForegroundColor Yellow
try {
    $redisResult = docker exec nafaplace-redis redis-cli ping
    if ($redisResult -eq "PONG") {
        Write-Host "✅ Redis - Connecté" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis - Non connecté" -ForegroundColor Red
        $allTestsPassed = $false
    }
} catch {
    Write-Host "❌ Redis - Erreur de connexion" -ForegroundColor Red
    $allTestsPassed = $false
}

# Test 5: Tester les APIs avec des données GNF
Write-Host "`n💰 Test 5: Fonctionnalités GNF" -ForegroundColor Yellow

# Test Catalog API - Vérifier les prix en GNF
try {
    $catalogResponse = Invoke-RestMethod -Uri "http://localhost:5243/api/products" -Method Get -TimeoutSec 10
    if ($catalogResponse -and $catalogResponse.Count -gt 0) {
        $firstProduct = $catalogResponse[0]
        if ($firstProduct.currency -eq "GNF" -or $firstProduct.Currency -eq "GNF") {
            Write-Host "✅ Catalog API - Produits en GNF" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Catalog API - Devise non GNF: $($firstProduct.currency)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ Catalog API - Aucun produit trouvé" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Catalog API - Erreur lors du test GNF" -ForegroundColor Red
    $allTestsPassed = $false
}

# Test Cart API - Vérifier les calculs GNF
try {
    $cartResponse = Invoke-RestMethod -Uri "http://localhost:5003/api/cart/test-user/summary" -Method Get -TimeoutSec 10
    if ($cartResponse.currency -eq "GNF") {
        Write-Host "✅ Cart API - Calculs en GNF" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Cart API - Devise: $($cartResponse.currency)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Cart API - Panier vide ou erreur (normal)" -ForegroundColor Yellow
}

# Test 6: Vérifier les applications web
Write-Host "`n🌐 Test 6: Applications Web" -ForegroundColor Yellow
$webApps = @(
    @{Name="Web App"; Url="http://localhost:8080"},
    @{Name="Admin Portal"; Url="http://localhost:8081"},
    @{Name="Seller Portal"; Url="http://localhost:8082"}
)

foreach ($app in $webApps) {
    try {
        $response = Invoke-WebRequest -Uri $app.Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($app.Name) - Accessible" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($app.Name) - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($app.Name) - Non accessible" -ForegroundColor Red
        $allTestsPassed = $false
    }
}

# Test 7: Vérifier Swagger UI
Write-Host "`n📚 Test 7: Documentation Swagger" -ForegroundColor Yellow
$swaggerEndpoints = @(
    "http://localhost:5000/swagger",
    "http://localhost:5155/swagger",
    "http://localhost:5243/swagger",
    "http://localhost:5006/swagger",
    "http://localhost:5007/swagger"
)

foreach ($swagger in $swaggerEndpoints) {
    try {
        $response = Invoke-WebRequest -Uri $swagger -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Swagger UI - $swagger" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️ Swagger UI - $swagger non accessible" -ForegroundColor Yellow
    }
}

# Résumé final
Write-Host "`n" -NoNewline
if ($allTestsPassed) {
    Write-Host "🎉 VALIDATION RÉUSSIE!" -ForegroundColor Green
    Write-Host "Toutes les fonctionnalités sont opérationnelles" -ForegroundColor Green
    Write-Host "`n🚀 Votre plateforme NafaPlace est prête!" -ForegroundColor Cyan
    Write-Host "- API Gateway: http://localhost:5000/swagger" -ForegroundColor White
    Write-Host "- Web App: http://localhost:8080" -ForegroundColor White
    Write-Host "- Admin Portal: http://localhost:8081" -ForegroundColor White
    Write-Host "- Seller Portal: http://localhost:8082" -ForegroundColor White
} else {
    Write-Host "⚠️ VALIDATION PARTIELLE" -ForegroundColor Yellow
    Write-Host "Certains services peuvent nécessiter plus de temps pour démarrer" -ForegroundColor Yellow
    Write-Host "Consultez les logs: docker-compose logs -f" -ForegroundColor White
}

Write-Host "`n💰 Fonctionnalités GNF activées:" -ForegroundColor Cyan
Write-Host "- Prix en Francs Guinéens (GNF)" -ForegroundColor White
Write-Host "- Livraison gratuite > 500,000 GNF" -ForegroundColor White
Write-Host "- Frais de livraison: 25,000 GNF" -ForegroundColor White
Write-Host "- TVA: 18%" -ForegroundColor White
