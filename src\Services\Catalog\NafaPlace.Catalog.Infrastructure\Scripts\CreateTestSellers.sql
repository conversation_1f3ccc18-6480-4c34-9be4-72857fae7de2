-- Script pour créer des vendeurs de test et les lier aux utilisateurs existants
-- Ce script doit être exécuté sur la base de données Catalog

-- 1. Vérifier les vendeurs existants
SELECT * FROM "Sellers";

-- 2. Supprimer les vendeurs existants pour éviter les doublons (optionnel)
-- DELETE FROM "Sellers";

-- 3. Insérer des vendeurs de test liés aux utilisateurs
-- Nous supposons que les utilisateurs avec les IDs 1, 2, 3, 4, 5 existent dans la base Identity

INSERT INTO "Sellers" ("Name", "Email", "PhoneNumber", "Address", "IsActive", "UserId", "CreatedAt", "UpdatedAt")
VALUES 
    ('Électronique Mali', '<EMAIL>', '+223 70 12 34 56', '123 Rue Bamako, Mali', true, '1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Mode Africaine', '<EMAIL>', '+223 78 45 67 89', '45 <PERSON> Dakar, Sénégal', true, '2', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Alimentation Bio', '<EMAIL>', '+223 76 98 76 54', '67 Boulevard Conakry, Guinée', true, '3', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Cosmétiques Naturels', '<EMAIL>', '+223 77 11 22 33', '89 Place Ouagadougou, Burkina Faso', true, '4', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Artisanat Local', '<EMAIL>', '+223 79 44 55 66', '12 Marché Central, Abidjan', true, '5', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT ("Email") DO NOTHING;

-- 4. Vérifier les vendeurs créés
SELECT 
    s."Id",
    s."Name",
    s."Email",
    s."UserId",
    s."IsActive",
    s."CreatedAt"
FROM "Sellers" s
ORDER BY s."Id";

-- 5. Compter le nombre de vendeurs
SELECT COUNT(*) as "NombreDeVendeurs" FROM "Sellers";
