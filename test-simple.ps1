# Test simple des fonctionnalites NafaPlace
Write-Host "=== Test des Fonctionnalites NafaPlace ===" -ForegroundColor Green

# Variables
$randomId = Get-Random -Minimum 1000 -Maximum 9999
$userEmail = "testuser$<EMAIL>"
$userUsername = "testuser$randomId"
$sellerEmail = "testseller$<EMAIL>"
$sellerUsername = "testseller$randomId"

# Test 1: Creation utilisateur
Write-Host "`nTest 1: Creation utilisateur" -ForegroundColor Yellow
try {
    $userBody = @{
        email = $userEmail
        username = $userUsername
        password = "User123!"
        firstName = "Test"
        lastName = "User"
    } | ConvertTo-Json

    $userResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/register" -Method POST -Body $userBody -ContentType "application/json"
    Write-Host "OK - Utilisateur cree: $userUsername" -ForegroundColor Green
    
    # Test connexion utilisateur
    Start-Sleep -Seconds 1
    $loginBody = @{
        email = $userEmail
        password = "User123!"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "OK - Connexion utilisateur reussie" -ForegroundColor Green
    
} catch {
    Write-Host "ERREUR - Creation/connexion utilisateur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Creation vendeur
Write-Host "`nTest 2: Creation vendeur" -ForegroundColor Yellow
try {
    $sellerBody = @{
        email = $sellerEmail
        username = $sellerUsername
        password = "Seller123!"
        firstName = "Test"
        lastName = "Seller"
        companyName = "Test Company $randomId"
        phoneNumber = "+224123456789"
    } | ConvertTo-Json

    $sellerResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/register-seller" -Method POST -Body $sellerBody -ContentType "application/json"
    Write-Host "OK - Vendeur cree: $sellerUsername" -ForegroundColor Green
    
    # Test connexion vendeur
    Start-Sleep -Seconds 1
    $loginBody = @{
        email = $sellerEmail
        password = "Seller123!"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "OK - Connexion vendeur reussie" -ForegroundColor Green
    
} catch {
    Write-Host "ERREUR - Creation/connexion vendeur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test admin
Write-Host "`nTest 3: Connexion admin" -ForegroundColor Yellow
try {
    $adminBody = @{
        email = "<EMAIL>"
        password = "Admin123!"
    } | ConvertTo-Json

    $adminResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $adminBody -ContentType "application/json"
    Write-Host "OK - Connexion admin reussie" -ForegroundColor Green
    
} catch {
    Write-Host "ERREUR - Connexion admin: $($_.Exception.Message)" -ForegroundColor Red
    
    # Test avec username au lieu d'email
    try {
        $adminBody2 = @{
            username = "admin"
            password = "Admin123!"
        } | ConvertTo-Json

        $adminResponse2 = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $adminBody2 -ContentType "application/json"
        Write-Host "OK - Connexion admin avec username reussie" -ForegroundColor Green
        
    } catch {
        Write-Host "ERREUR - Connexion admin avec username: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 4: Test catalog
Write-Host "`nTest 4: Test catalog" -ForegroundColor Yellow
try {
    $productsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/catalog/v1/products"
    Write-Host "OK - Produits recuperes: $($productsResponse.Count) produits" -ForegroundColor Green
    
    $categoriesResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/catalog/v1/categories"
    Write-Host "OK - Categories recuperees: $($categoriesResponse.Count) categories" -ForegroundColor Green
    
} catch {
    Write-Host "ERREUR - Test catalog: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Resume ===" -ForegroundColor Green
Write-Host "Comptes crees pour tests manuels:" -ForegroundColor White
Write-Host "Utilisateur: $userEmail / User123!" -ForegroundColor Cyan
Write-Host "Vendeur: $sellerEmail / Seller123!" -ForegroundColor Cyan
Write-Host "Admin: <EMAIL> / Admin123!" -ForegroundColor Yellow

Write-Host "`nPortails web:" -ForegroundColor White
Write-Host "Main Web: http://localhost:8080" -ForegroundColor Cyan
Write-Host "Admin Portal: http://localhost:8081" -ForegroundColor Cyan
Write-Host "Seller Portal: http://localhost:8082" -ForegroundColor Cyan
