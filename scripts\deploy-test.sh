#!/bin/bash

# Script de déploiement de test pour NafaPlace sur fly.io

set -e

echo "🚀 Déploiement de NafaPlace en environnement de test..."

# Vérifier que fly CLI est installé
if ! command -v flyctl &> /dev/null; then
    echo "❌ Fly CLI n'est pas installé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/"
    exit 1
fi

# Vérifier l'authentification
if ! flyctl auth whoami &> /dev/null; then
    echo "❌ Vous n'êtes pas connecté à Fly.io. Exécutez 'flyctl auth login'"
    exit 1
fi

echo "📦 Création de l'application de test si elle n'existe pas..."
flyctl apps create nafaplace-test --org personal || echo "L'application existe déjà"

echo "🗄️ Création de la base de données PostgreSQL de test..."
flyctl postgres create --name nafaplace-test-db --region cdg --vm-size shared-cpu-1x --volume-size 10 || echo "La base de données existe déjà"

echo "🔗 Attachement de la base de données à l'application..."
flyctl postgres attach --app nafaplace-test nafaplace-test-db || echo "Base de données déjà attachée"

echo "🔐 Configuration des secrets..."
flyctl secrets set \
  CLOUDINARY_CLOUD_NAME="$CLOUDINARY_CLOUD_NAME" \
  CLOUDINARY_API_KEY="$CLOUDINARY_API_KEY" \
  CLOUDINARY_API_SECRET="$CLOUDINARY_API_SECRET" \
  STRIPE_PUBLISHABLE_KEY="$STRIPE_PUBLISHABLE_KEY" \
  STRIPE_SECRET_KEY="$STRIPE_SECRET_KEY" \
  JWT_SECRET="NafaPlaceTestSecretKey2025ForStagingEnvironment" \
  --app nafaplace-test

echo "🚀 Déploiement de l'application..."
flyctl deploy --app nafaplace-test --remote-only

echo "⏳ Attente du démarrage de l'application..."
sleep 30

echo "🏥 Vérification de l'état de l'application..."
flyctl status --app nafaplace-test

echo "📊 Affichage des logs récents..."
flyctl logs --app nafaplace-test --lines 20

echo "✅ Déploiement terminé!"
echo "🌐 Votre application de test est disponible à: https://nafaplace-test.fly.dev"
echo "📊 Surveillez les logs avec: flyctl logs --app nafaplace-test -f"
