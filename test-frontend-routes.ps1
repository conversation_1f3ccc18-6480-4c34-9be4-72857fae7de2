# Script de test des routes frontend pour les trois portails
Write-Host "=== Test des routes Frontend NafaPlace ===" -ForegroundColor Green

# Configuration des URLs des portails
$MAIN_WEB = "http://localhost:8080"
$ADMIN_PORTAL = "http://localhost:8081"
$SELLER_PORTAL = "http://localhost:8082"

# Fonction pour tester une route frontend
function Test-FrontendRoute {
    param(
        [string]$Url,
        [string]$Description,
        [string]$Portal
    )
    
    Write-Host "`n--- Test: $Description ($Portal) ---" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 10
        Write-Host "Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
        
        # Vérifier si c'est une page HTML valide
        if ($response.Content -like "*<html*" -or $response.Content -like "*<!DOCTYPE*") {
            Write-Host "Content: Valid HTML page detected" -ForegroundColor Gray
        } else {
            Write-Host "Content: Non-HTML response" -ForegroundColor Yellow
        }
        
        return $true
    }
    catch {
        Write-Host "Status: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 1. Test du portail principal (Main Web)
Write-Host "`n=== 1. MAIN WEB PORTAL TESTS ===" -ForegroundColor Magenta
Test-FrontendRoute -Url "$MAIN_WEB/" -Description "Homepage" -Portal "Main Web"
Test-FrontendRoute -Url "$MAIN_WEB/products" -Description "Products Page" -Portal "Main Web"
Test-FrontendRoute -Url "$MAIN_WEB/categories" -Description "Categories Page" -Portal "Main Web"
Test-FrontendRoute -Url "$MAIN_WEB/login" -Description "Login Page" -Portal "Main Web"
Test-FrontendRoute -Url "$MAIN_WEB/register" -Description "Register Page" -Portal "Main Web"
Test-FrontendRoute -Url "$MAIN_WEB/cart" -Description "Cart Page" -Portal "Main Web"

# 2. Test du portail admin
Write-Host "`n=== 2. ADMIN PORTAL TESTS ===" -ForegroundColor Magenta
Test-FrontendRoute -Url "$ADMIN_PORTAL/" -Description "Admin Homepage" -Portal "Admin Portal"
Test-FrontendRoute -Url "$ADMIN_PORTAL/login" -Description "Admin Login Page" -Portal "Admin Portal"
Test-FrontendRoute -Url "$ADMIN_PORTAL/dashboard" -Description "Admin Dashboard" -Portal "Admin Portal"
Test-FrontendRoute -Url "$ADMIN_PORTAL/users" -Description "User Management" -Portal "Admin Portal"
Test-FrontendRoute -Url "$ADMIN_PORTAL/products" -Description "Product Management" -Portal "Admin Portal"
Test-FrontendRoute -Url "$ADMIN_PORTAL/categories" -Description "Category Management" -Portal "Admin Portal"
Test-FrontendRoute -Url "$ADMIN_PORTAL/orders" -Description "Order Management" -Portal "Admin Portal"

# 3. Test du portail vendeur
Write-Host "`n=== 3. SELLER PORTAL TESTS ===" -ForegroundColor Magenta
Test-FrontendRoute -Url "$SELLER_PORTAL/" -Description "Seller Homepage" -Portal "Seller Portal"
Test-FrontendRoute -Url "$SELLER_PORTAL/login" -Description "Seller Login Page" -Portal "Seller Portal"
Test-FrontendRoute -Url "$SELLER_PORTAL/dashboard" -Description "Seller Dashboard" -Portal "Seller Portal"
Test-FrontendRoute -Url "$SELLER_PORTAL/products" -Description "Seller Products" -Portal "Seller Portal"
Test-FrontendRoute -Url "$SELLER_PORTAL/orders" -Description "Seller Orders" -Portal "Seller Portal"
Test-FrontendRoute -Url "$SELLER_PORTAL/statistics" -Description "Seller Statistics" -Portal "Seller Portal"

Write-Host "`n=== Test Frontend terminé ===" -ForegroundColor Green

# Résumé des configurations API dans chaque portail
Write-Host "`n=== RÉSUMÉ DES CONFIGURATIONS API ===" -ForegroundColor Magenta

Write-Host "`nMain Web Portal Configuration:" -ForegroundColor Cyan
Write-Host "- Identity API: https://nafaplace-test.fly.dev/api/identity"
Write-Host "- Catalog API: https://nafaplace-test.fly.dev/api/catalog"
Write-Host "- Cart API: https://nafaplace-test.fly.dev/api/cart"
Write-Host "- Order API: https://nafaplace-test.fly.dev/api/orders"

Write-Host "`nAdmin Portal Configuration:" -ForegroundColor Cyan
Write-Host "- Identity API: https://nafaplace-test.fly.dev/api/identity"
Write-Host "- Catalog API: https://nafaplace-test.fly.dev/api/catalog"
Write-Host "- Order API: https://nafaplace-test.fly.dev/api/orders"

Write-Host "`nSeller Portal Configuration:" -ForegroundColor Cyan
Write-Host "- Identity API: https://nafaplace-test.fly.dev/api/identity"
Write-Host "- Catalog API: https://nafaplace-test.fly.dev/api/catalog"
Write-Host "- Review API: https://nafaplace-test.fly.dev/api/reviews"
