{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApiSettings": {"BaseUrl": "http://nafaplace-test.fly.dev:8080", "CatalogApiUrl": "http://nafaplace-test.fly.dev:8080/api/catalog", "IdentityApiUrl": "http://nafaplace-test.fly.dev:8080/api/identity", "OrderApiUrl": "http://nafaplace-test.fly.dev:8080/api/orders", "CartApiUrl": "http://nafaplace-test.fly.dev:8080/api/cart", "PaymentApiUrl": "http://nafaplace-test.fly.dev:8080/api/payments", "ReviewApiUrl": "http://nafaplace-test.fly.dev:8080/api/reviews"}}