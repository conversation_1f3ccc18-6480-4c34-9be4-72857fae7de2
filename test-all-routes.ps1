# Script de test complet pour toutes les routes NafaPlace
Write-Host "=== Test des routes NafaPlace ===" -ForegroundColor Green

# Configuration des URLs
$API_GATEWAY = "http://localhost:5000"
$IDENTITY_API = "http://localhost:5155"
$CATALOG_API = "http://localhost:5243"
$CART_API = "http://localhost:5003"
$ORDER_API = "http://localhost:5004"
$PAYMENT_API = "http://localhost:5005"
$REVIEW_API = "http://localhost:5006"

# Fonction pour tester une route
function Test-Route {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [string]$Body = $null,
        [hashtable]$Headers = @{},
        [string]$Description
    )
    
    Write-Host "`n--- Test: $Description ---" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Cyan
    Write-Host "Method: $Method" -ForegroundColor Cyan
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        Write-Host "Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
        
        # Afficher un aperçu du contenu si c'est du JSON
        if ($response.Content.Length -lt 500) {
            Write-Host "Response: $($response.Content)" -ForegroundColor Gray
        } else {
            Write-Host "Response: [Large content - $($response.Content.Length) chars]" -ForegroundColor Gray
        }
        
        return $true
    }
    catch {
        Write-Host "Status: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 1. Test de l'API Gateway
Write-Host "`n=== 1. API GATEWAY TESTS ===" -ForegroundColor Magenta
Test-Route -Url "$API_GATEWAY/" -Description "API Gateway Health Check"
Test-Route -Url "$API_GATEWAY/health" -Description "API Gateway Health Endpoint"

# 2. Test de l'API Identity (Authentication)
Write-Host "`n=== 2. IDENTITY API TESTS ===" -ForegroundColor Magenta

# Test de connexion admin
$loginBody = @{
    username = "<EMAIL>"
    password = "Admin123!"
} | ConvertTo-Json

$loginSuccess = Test-Route -Url "$API_GATEWAY/api/identity/auth/login" -Method "POST" -Body $loginBody -Description "Admin Login via Gateway"

if ($loginSuccess) {
    # Récupérer le token pour les tests suivants
    $loginResponse = Invoke-WebRequest -Uri "$API_GATEWAY/api/identity/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.accessToken
    $authHeaders = @{ "Authorization" = "Bearer $token" }
    
    # Tests avec authentification
    Test-Route -Url "$API_GATEWAY/api/identity/users/me" -Headers $authHeaders -Description "Get Current User Profile (users/me)"
    Test-Route -Url "$API_GATEWAY/api/identity/users/profile" -Headers $authHeaders -Description "Get Current User Profile (users/profile)"
    Test-Route -Url "$API_GATEWAY/api/identity/auth/me" -Headers $authHeaders -Description "Get Current User Profile (auth/me)"
    Test-Route -Url "$API_GATEWAY/api/identity/users" -Headers $authHeaders -Description "Get All Users (Admin)"
    Test-Route -Url "$API_GATEWAY/api/identity/roles" -Headers $authHeaders -Description "Get All Roles"
    Test-Route -Url "$API_GATEWAY/api/identity/users/1" -Headers $authHeaders -Description "Get User by ID"
}

# 3. Test de l'API Catalog
Write-Host "`n=== 3. CATALOG API TESTS ===" -ForegroundColor Magenta
Test-Route -Url "$API_GATEWAY/api/catalog/v1/products" -Description "Get All Products"
Test-Route -Url "$API_GATEWAY/api/catalog/v1/categories" -Description "Get All Categories"
Test-Route -Url "$API_GATEWAY/api/catalog/v1/products?page=1&pageSize=5" -Description "Get Products with Pagination"

# Test direct de l'API Catalog
Test-Route -Url "$CATALOG_API/v1/products" -Description "Direct Catalog API - Get Products"
Test-Route -Url "$CATALOG_API/v1/categories" -Description "Direct Catalog API - Get Categories"

# 4. Test des autres APIs
Write-Host "`n=== 4. OTHER APIS TESTS ===" -ForegroundColor Magenta
Test-Route -Url "$API_GATEWAY/api/cart/health" -Description "Cart API Health"
Test-Route -Url "$API_GATEWAY/api/orders/health" -Description "Order API Health"
Test-Route -Url "$API_GATEWAY/api/payments/health" -Description "Payment API Health"
Test-Route -Url "$API_GATEWAY/api/reviews/health" -Description "Review API Health"

Write-Host "`n=== Test terminé ===" -ForegroundColor Green
