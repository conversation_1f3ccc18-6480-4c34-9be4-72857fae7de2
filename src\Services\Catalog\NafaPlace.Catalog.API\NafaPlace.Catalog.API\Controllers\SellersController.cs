using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Seller;
using NafaPlace.Catalog.Application.Services;
using NafaPlace.Catalog.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class SellersController : ControllerBase
    {
        private readonly ISellerService _sellerService;
        private readonly CatalogDbContext _context;

        public SellersController(ISellerService sellerService, CatalogDbContext context)
        {
            _sellerService = sellerService;
            _context = context;
        }

        /// <summary>
        /// Récupère la liste de tous les vendeurs
        /// </summary>
        /// <returns>Liste des vendeurs</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllSellers()
        {
            try
            {
                var sellers = await _context.Sellers
                    .Select(s => new
                    {
                        Id = s.Id,
                        Name = s.Name ?? "Vendeur sans nom",
                        Email = s.Email ?? "",
                        PhoneNumber = s.PhoneNumber ?? "",
                        Address = s.Address ?? "",
                        IsActive = s.IsActive,
                        IsVerified = true // Valeur par défaut pour la compatibilité
                    })
                    .ToListAsync();

                return Ok(sellers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des vendeurs", error = ex.Message });
            }
        }

        /// <summary>
        /// Récupère un vendeur par son ID utilisateur
        /// </summary>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <returns>Informations du vendeur</returns>
        [HttpGet("by-user/{userId:int}")]
        public async Task<IActionResult> GetSellerByUserId(int userId)
        {
            try
            {
                var seller = await _context.Sellers
                    .Where(s => s.UserId == userId.ToString())
                    .Select(s => new
                    {
                        Id = s.Id,
                        Name = s.Name ?? "Vendeur sans nom",
                        Email = s.Email ?? "",
                        PhoneNumber = s.PhoneNumber ?? "",
                        Address = s.Address ?? "",
                        IsActive = s.IsActive,
                        UserId = s.UserId
                    })
                    .FirstOrDefaultAsync();

                if (seller == null)
                {
                    return NotFound(new { message = $"Aucun vendeur trouvé pour l'utilisateur {userId}" });
                }

                return Ok(seller);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération du vendeur", error = ex.Message });
            }
        }

        [HttpPost("profile-picture")]
        public async Task<IActionResult> UploadProfilePicture([FromBody] UploadProfilePictureRequest request)
        {
            var result = await _sellerService.UploadProfilePictureAsync(request);
            if (result == null)
            {
                return NotFound("Seller not found.");
            }
            return Ok(result);
        }
    }
}
