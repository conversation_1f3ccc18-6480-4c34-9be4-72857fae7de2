Write-Host "=== Test d'authentification pour différents utilisateurs ===" -ForegroundColor Cyan

# Test avec admin (qui fonctionne)
Write-Host "<NAME_EMAIL>..." -ForegroundColor Yellow
try {
    $body = @{
        Email = "<EMAIL>"
        Username = ""
        Password = "Admin123!"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $body -TimeoutSec 10
    Write-Host "SUCCESS: Admin authentication réussie!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Admin authentication échouée" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test avec différents mots de passe pour testseller1732
Write-Host "<NAME_EMAIL> et différents mots de passe..." -ForegroundColor Yellow

$passwords = @("TestSeller123!", "Seller123!", "testseller123", "TestSeller123", "Admin123!")

foreach ($password in $passwords) {
    Write-Host "Essai avec mot de passe: $password" -ForegroundColor Cyan
    try {
        $body = @{
            Email = "<EMAIL>"
            Username = ""
            Password = $password
        } | ConvertTo-Json
        
        $response = Invoke-WebRequest -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $body -TimeoutSec 10
        Write-Host "SUCCESS: Seller authentication réussie avec $password!" -ForegroundColor Green
        Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
        break
    } catch {
        Write-Host "FAILED: Mot de passe $password ne fonctionne pas" -ForegroundColor Red
        if ($_.Exception.Response.StatusCode) {
            Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
}

Write-Host ""

# Test avec testuser1732
Write-Host "<NAME_EMAIL>..." -ForegroundColor Yellow
try {
    $body = @{
        Email = "<EMAIL>"
        Username = ""
        Password = "TestUser123!"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $body -TimeoutSec 10
    Write-Host "SUCCESS: User authentication réussie!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: User authentication échouée" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response.StatusCode) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Test terminé ===" -ForegroundColor Cyan
