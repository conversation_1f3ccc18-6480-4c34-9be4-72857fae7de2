Write-Host "=== Test d'authentification depuis les portails ===" -ForegroundColor Cyan

# Test depuis le Main Web Portal (port 8080)
Write-Host "Test authentification depuis Main Web Portal..." -ForegroundColor Yellow
try {
    $headers = @{
        'Origin' = 'http://localhost:8080'
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $body = @{
        Email = "<EMAIL>"
        Username = ""
        Password = "Admin123!"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5155/api/auth/login" -Method POST -Headers $headers -Body $body -TimeoutSec 10
    Write-Host "SUCCESS: Authentification depuis Main Web Portal réussie!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Vérifier le token
    $responseContent = $response.Content | ConvertFrom-Json
    if ($responseContent.token) {
        Write-Host "Token reçu: $($responseContent.token.Length) caractères" -ForegroundColor Green
    }
} catch {
    Write-Host "FAILED: Authentification depuis Main Web Portal échouée" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""

# Test depuis l'Admin Portal (port 8081)
Write-Host "Test authentification depuis Admin Portal..." -ForegroundColor Yellow
try {
    $headers = @{
        'Origin' = 'http://localhost:8081'
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $body = @{
        Email = "<EMAIL>"
        Username = ""
        Password = "Admin123!"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5155/api/auth/login" -Method POST -Headers $headers -Body $body -TimeoutSec 10
    Write-Host "SUCCESS: Authentification depuis Admin Portal réussie!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Vérifier le token
    $responseContent = $response.Content | ConvertFrom-Json
    if ($responseContent.token) {
        Write-Host "Token reçu: $($responseContent.token.Length) caractères" -ForegroundColor Green
    }
} catch {
    Write-Host "FAILED: Authentification depuis Admin Portal échouée" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""

# Test depuis le Seller Portal (port 8082)
Write-Host "Test authentification depuis Seller Portal..." -ForegroundColor Yellow
try {
    $headers = @{
        'Origin' = 'http://localhost:8082'
        'Content-Type' = 'application/json'
        'Accept' = 'application/json'
    }
    
    $body = @{
        Email = "<EMAIL>"
        Username = ""
        Password = "TestSeller123!"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5155/api/auth/login" -Method POST -Headers $headers -Body $body -TimeoutSec 10
    Write-Host "SUCCESS: Authentification depuis Seller Portal réussie!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Vérifier le token
    $responseContent = $response.Content | ConvertFrom-Json
    if ($responseContent.token) {
        Write-Host "Token reçu: $($responseContent.token.Length) caractères" -ForegroundColor Green
    }
} catch {
    Write-Host "FAILED: Authentification depuis Seller Portal échouée" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Test terminé ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "Si tous les tests sont SUCCESS, le problème est dans le code JavaScript des portails." -ForegroundColor Yellow
Write-Host "Si les tests échouent, le problème est dans la configuration CORS ou l'API." -ForegroundColor Yellow
