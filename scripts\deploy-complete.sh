#!/bin/bash

# Script de déploiement complet pour NafaPlace sur fly.io
# Ce script configure et déploie automatiquement l'application de test

set -e

echo "🚀 Déploiement automatique de NafaPlace sur fly.io..."

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher des messages colorés
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifications préliminaires
log_info "Vérification des prérequis..."

if ! command -v flyctl &> /dev/null; then
    log_error "Fly CLI n'est pas installé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/"
    exit 1
fi

if ! flyctl auth whoami &> /dev/null; then
    log_error "Vous n'êtes pas connecté à Fly.io. Exécutez 'flyctl auth login'"
    exit 1
fi

if ! command -v git &> /dev/null; then
    log_error "Git n'est pas installé."
    exit 1
fi

log_success "Tous les prérequis sont satisfaits"

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "NafaPlace.sln" ]; then
    log_error "Ce script doit être exécuté depuis la racine du projet NafaPlace"
    exit 1
fi

# Vérifier les variables d'environnement nécessaires
if [ -z "$CLOUDINARY_CLOUD_NAME" ] || [ -z "$CLOUDINARY_API_KEY" ] || [ -z "$CLOUDINARY_API_SECRET" ]; then
    log_warning "Variables Cloudinary manquantes. Vérifiez CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, CLOUDINARY_API_SECRET"
fi

if [ -z "$STRIPE_PUBLISHABLE_KEY" ] || [ -z "$STRIPE_SECRET_KEY" ]; then
    log_warning "Variables Stripe manquantes. Vérifiez STRIPE_PUBLISHABLE_KEY, STRIPE_SECRET_KEY"
fi

# Étape 1: Build et test local
log_info "Construction et test de l'application..."
dotnet restore
dotnet build --configuration Release
log_success "Build réussi"

# Étape 2: Création de l'application fly.io
log_info "Création de l'application de test sur fly.io..."
flyctl apps create nafaplace-test --org personal 2>/dev/null || log_warning "L'application existe déjà"

# Étape 3: Création de la base de données PostgreSQL
log_info "Configuration de la base de données PostgreSQL..."
flyctl postgres create --name nafaplace-test-db --region cdg --vm-size shared-cpu-1x --volume-size 10 2>/dev/null || log_warning "La base de données existe déjà"

# Étape 4: Attachement de la base de données
log_info "Attachement de la base de données..."
flyctl postgres attach --app nafaplace-test nafaplace-test-db 2>/dev/null || log_warning "Base de données déjà attachée"

# Étape 5: Configuration des secrets
log_info "Configuration des secrets d'application..."
flyctl secrets set \
  CLOUDINARY_CLOUD_NAME="${CLOUDINARY_CLOUD_NAME:-demo}" \
  CLOUDINARY_API_KEY="${CLOUDINARY_API_KEY:-123456789}" \
  CLOUDINARY_API_SECRET="${CLOUDINARY_API_SECRET:-demo_secret}" \
  STRIPE_PUBLISHABLE_KEY="${STRIPE_PUBLISHABLE_KEY:-pk_test_demo}" \
  STRIPE_SECRET_KEY="${STRIPE_SECRET_KEY:-sk_test_demo}" \
  JWT_SECRET="NafaPlaceTestSecretKey2025ForStagingEnvironment" \
  --app nafaplace-test

log_success "Secrets configurés"

# Étape 6: Déploiement
log_info "Déploiement de l'application..."
flyctl deploy --app nafaplace-test --remote-only

log_success "Déploiement terminé"

# Étape 7: Vérifications post-déploiement
log_info "Vérifications post-déploiement..."
sleep 30

log_info "État de l'application:"
flyctl status --app nafaplace-test

log_info "Logs récents:"
flyctl logs --app nafaplace-test --lines 10

# Étape 8: Test de santé
log_info "Test de l'endpoint de santé..."
if curl -f -s https://nafaplace-test.fly.dev/health > /dev/null; then
    log_success "Endpoint de santé accessible"
else
    log_warning "Endpoint de santé non accessible (l'application peut encore démarrer)"
fi

# Résumé final
echo ""
echo "🎉 Déploiement terminé avec succès!"
echo ""
echo "📊 Informations de déploiement:"
echo "   🌐 URL de test: https://nafaplace-test.fly.dev"
echo "   🏥 Santé: https://nafaplace-test.fly.dev/health"
echo "   📊 Logs: flyctl logs --app nafaplace-test -f"
echo "   ⚙️  Status: flyctl status --app nafaplace-test"
echo ""
echo "🔧 Commandes utiles:"
echo "   flyctl ssh console --app nafaplace-test"
echo "   flyctl scale count 1 --app nafaplace-test"
echo "   flyctl restart --app nafaplace-test"
echo ""
log_success "NafaPlace est maintenant déployé en environnement de test!"
